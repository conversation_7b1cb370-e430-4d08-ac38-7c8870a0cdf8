#!/bin/bash

echo "🔧 Installing Python dependencies..."
pip install flask mysql-connector-python flask-sqlalchemy pymysql

echo "⏳ Waiting for database to be ready..."
sleep 15

echo "🚀 Starting Flask application..."
cd /app

# Try to start the test app first
if [ -f "test_app.py" ]; then
    echo "📱 Starting test_app.py..."
    python test_app.py
else
    echo "📱 test_app.py not found, starting app.py..."
    python app.py
fi
