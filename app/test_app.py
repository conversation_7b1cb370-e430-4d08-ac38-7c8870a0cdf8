from flask import Flask
import os

app = Flask(__name__)

@app.route('/')
def hello():
    return '<h1>Furniture Store Management System</h1><p>Flask is working!</p>'

@app.route('/test')
def test():
    return '<h2>Test Route</h2><p>Database URL: ' + os.environ.get('DATABASE_URL', 'Not set') + '</p>'

if __name__ == '__main__':
    print('Starting test Flask application...')
    app.run(host='0.0.0.0', port=5000, debug=True)
