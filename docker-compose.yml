version: '3.8'

services:
  # Base de données MySQL
  db:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: admin123
      MYSQL_DATABASE: gestion_meubles
      MYSQL_USER: frappe_user
      MYSQL_PASSWORD: frappe123
    volumes:
      - db_data:/var/lib/mysql
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password

  # Application Web Flask
  web:
    image: python:3.9-slim
    restart: always
    working_dir: /app
    volumes:
      - ./app:/app
    ports:
      - "5000:5000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=mysql://frappe_user:frappe123@db:3306/gestion_meubles
    command: >
    command: bash /app/start.sh

  # Interface d'administration phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_USER: root
      PMA_PASSWORD: admin123
    depends_on:
      - db

volumes:
  db_data:
