-- Script d'initialisation pour la base de données Gestion Meubles
CREATE DATABASE IF NOT EXISTS gestion_meubles CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'frappe'@'%' IDENTIFIED BY 'frappe123';
GRANT ALL PRIVILEGES ON gestion_meubles.* TO 'frappe'@'%';
GRANT ALL PRIVILEGES ON `_gestion_meubles%`.* TO 'frappe'@'%';
GRANT ALL PRIVILEGES ON `test_gestion_meubles`.* TO 'frappe'@'%';
FLUSH PRIVILEGES;
SELECT 'Base de données Gestion Meubles initialisée avec succès!' as message;
