# 🏠 Gestion Meubles - Système de Gestion avec Frappe/ERPNext

## 🚀 Démarrage Rapide

1. **Démarrer l'application**
```bash
docker-compose up -d
```

2. **Accéder à l'application**
- URL: http://localhost:8000
- Utilisateur: Administrator
- Mot de passe: admin123

3. **Première configuration**
- Configurer votre entreprise
- Ajouter vos produits meubles
- Configurer les taxes

## 📦 Fonctionnalités ERPNext pour Meubles

- **Gestion Stock**: Produits avec variantes (dimensions, couleurs)
- **Ventes**: Factures PDF, paiements partiels
- **Achats**: Gestion fournisseurs, prix variables
- **Comptabilité**: Calcul bénéfices automatique
- **Rapports**: Analytics complets

## 🔧 Commandes Utiles

```bash
# Voir les logs
docker-compose logs -f frappe

# Arrêter
docker-compose down

# Redémarrer
docker-compose restart frappe
```
