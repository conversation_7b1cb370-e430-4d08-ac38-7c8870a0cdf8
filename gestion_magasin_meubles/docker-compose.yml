version: '3.8'

services:
  # Base de données MySQL pour compatibilité Windows/Linux
  db:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: magasin123
      MYSQL_DATABASE: magasin_meubles
      MYSQL_USER: magasin_user
      MYSQL_PASSWORD: magasin_pass
    volumes:
      - db_data:/var/lib/mysql
      - ./db-init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password --bind-address=0.0.0.0
    networks:
      - magasin_network

  # Application principale Flask
  web:
    build: .
    restart: always
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=mysql://magasin_user:magasin_pass@db:3306/magasin_meubles
      - SECRET_KEY=votre_cle_secrete_super_forte_2024
    volumes:
      - .:/app
      - ./uploads:/app/uploads
      - ./reports:/app/reports
    depends_on:
      - db
    networks:
      - magasin_network

  # Interface d'administration phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_USER: root
      PMA_PASSWORD: magasin123
    depends_on:
      - db
    networks:
      - magasin_network

volumes:
  db_data:

networks:
  magasin_network:
    driver: bridge
