from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from dotenv import load_dotenv
import os
from datetime import datetime, date
from decimal import Decimal
import uuid
import json

# Charger les variables d'environnement
load_dotenv()

# Importer les modèles
from models import db, Product, Category, Customer, Order, OrderItem, Payment, StockMovement, Expense, Settings
from pdf_generator import PDFGenerator

# Configuration de l'application
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///magasin.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))

# Initialiser les extensions
db.init_app(app)
migrate = Migrate(app, db)
CORS(app)

# Créer les dossiers nécessaires
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('reports', exist_ok=True)
os.makedirs('static/uploads', exist_ok=True)

@app.route('/')
def dashboard():
    """Page d'accueil avec tableau de bord"""
    try:
        # Statistiques générales
        total_products = Product.query.filter_by(is_active=True).count()
        low_stock_products = Product.query.filter(Product.stock_quantity <= Product.min_stock).count()
        pending_orders = Order.query.filter_by(status='pending').count()
        total_revenue_today = db.session.query(db.func.sum(Payment.amount)).filter(
            db.func.date(Payment.created_at) == date.today()
        ).scalar() or 0
        
        # Produits en rupture de stock
        low_stock_items = Product.query.filter(
            Product.stock_quantity <= Product.min_stock,
            Product.is_active == True
        ).limit(5).all()
        
        # Commandes récentes
        recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
        
        # Ventes du mois
        from sqlalchemy import extract
        monthly_sales = db.session.query(
            db.func.sum(Payment.amount)
        ).filter(
            extract('month', Payment.created_at) == datetime.now().month,
            extract('year', Payment.created_at) == datetime.now().year
        ).scalar() or 0
        
        return render_template('dashboard.html',
                             total_products=total_products,
                             low_stock_products=low_stock_products,
                             pending_orders=pending_orders,
                             total_revenue_today=total_revenue_today,
                             monthly_sales=monthly_sales,
                             low_stock_items=low_stock_items,
                             recent_orders=recent_orders)
    except Exception as e:
        flash(f'Erreur lors du chargement du tableau de bord: {str(e)}', 'error')
        return render_template('dashboard.html',
                             total_products=0,
                             low_stock_products=0,
                             pending_orders=0,
                             total_revenue_today=0,
                             monthly_sales=0,
                             low_stock_items=[],
                             recent_orders=[])

@app.route('/products')
def products():
    """Page de gestion des produits"""
    search = request.args.get('search', '')
    category_id = request.args.get('category', '')
    
    query = Product.query.filter_by(is_active=True)
    
    if search:
        query = query.filter(
            db.or_(
                Product.name.contains(search),
                Product.code.contains(search),
                Product.description.contains(search)
            )
        )
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.order_by(Product.name).all()
    categories = Category.query.filter_by(is_active=True).all()
    
    return render_template('products.html', products=products, categories=categories)

@app.route('/products/add', methods=['GET', 'POST'])
def add_product():
    """Ajouter un nouveau produit"""
    if request.method == 'POST':
        try:
            # Générer un code produit unique
            code = request.form.get('code')
            if not code:
                code = f"PROD{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            product = Product(
                code=code,
                name=request.form.get('name'),
                description=request.form.get('description'),
                category_id=request.form.get('category_id') or None,
                purchase_price=Decimal(request.form.get('purchase_price', 0)),
                sale_price=Decimal(request.form.get('sale_price', 0)),
                stock_quantity=int(request.form.get('stock_quantity', 0)),
                min_stock=int(request.form.get('min_stock', 5))
            )
            
            db.session.add(product)
            db.session.commit()
            
            # Enregistrer le mouvement de stock initial
            if product.stock_quantity > 0:
                movement = StockMovement(
                    product_id=product.id,
                    movement_type='in',
                    quantity=product.stock_quantity,
                    reference='initial_stock',
                    notes='Stock initial'
                )
                db.session.add(movement)
                db.session.commit()
            
            flash('Produit ajouté avec succès!', 'success')
            return redirect(url_for('products'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout du produit: {str(e)}', 'error')
    
    categories = Category.query.filter_by(is_active=True).all()
    return render_template('add_product.html', categories=categories)

@app.route('/products/<int:product_id>/edit', methods=['GET', 'POST'])
def edit_product(product_id):
    """Modifier un produit"""
    product = Product.query.get_or_404(product_id)
    
    if request.method == 'POST':
        try:
            old_stock = product.stock_quantity
            
            product.name = request.form.get('name')
            product.description = request.form.get('description')
            product.category_id = request.form.get('category_id') or None
            product.purchase_price = Decimal(request.form.get('purchase_price', 0))
            product.sale_price = Decimal(request.form.get('sale_price', 0))
            new_stock = int(request.form.get('stock_quantity', 0))
            product.min_stock = int(request.form.get('min_stock', 5))
            product.updated_at = datetime.utcnow()
            
            # Gérer le changement de stock
            if new_stock != old_stock:
                product.stock_quantity = new_stock
                movement_type = 'in' if new_stock > old_stock else 'out'
                quantity = abs(new_stock - old_stock)
                
                movement = StockMovement(
                    product_id=product.id,
                    movement_type=movement_type,
                    quantity=quantity,
                    reference='manual_adjustment',
                    notes=f'Ajustement manuel: {old_stock} → {new_stock}'
                )
                db.session.add(movement)
            
            db.session.commit()
            flash('Produit modifié avec succès!', 'success')
            return redirect(url_for('products'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la modification: {str(e)}', 'error')
    
    categories = Category.query.filter_by(is_active=True).all()
    return render_template('edit_product.html', product=product, categories=categories)

@app.route('/products/<int:product_id>/delete', methods=['POST'])
def delete_product(product_id):
    """Supprimer un produit (désactivation)"""
    try:
        product = Product.query.get_or_404(product_id)
        product.is_active = False
        product.updated_at = datetime.utcnow()
        db.session.commit()
        flash('Produit supprimé avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression: {str(e)}', 'error')
    
    return redirect(url_for('products'))

@app.route('/sales')
def sales():
    """Page de vente avec panier"""
    search = request.args.get('search', '')
    category_id = request.args.get('category', '')

    query = Product.query.filter_by(is_active=True).filter(Product.stock_quantity > 0)

    if search:
        query = query.filter(
            db.or_(
                Product.name.contains(search),
                Product.code.contains(search)
            )
        )

    if category_id:
        query = query.filter_by(category_id=category_id)

    products = query.order_by(Product.name).all()
    categories = Category.query.filter_by(is_active=True).all()
    customers = Customer.query.order_by(Customer.name).all()

    return render_template('sales.html', products=products, categories=categories, customers=customers)

@app.route('/api/products/search')
def api_search_products():
    """API pour rechercher des produits"""
    search = request.args.get('q', '')

    products = Product.query.filter(
        Product.is_active == True,
        Product.stock_quantity > 0,
        db.or_(
            Product.name.contains(search),
            Product.code.contains(search)
        )
    ).limit(10).all()

    return jsonify([{
        'id': p.id,
        'code': p.code,
        'name': p.name,
        'price': float(p.sale_price),
        'stock': p.stock_quantity
    } for p in products])

@app.route('/orders/create', methods=['POST'])
def create_order():
    """Créer une nouvelle commande"""
    try:
        data = request.get_json()

        # Créer le client si nécessaire
        customer_id = data.get('customer_id')
        if not customer_id and data.get('customer_name'):
            customer = Customer(
                name=data.get('customer_name'),
                phone=data.get('customer_phone', ''),
                address=data.get('customer_address', '')
            )
            db.session.add(customer)
            db.session.flush()
            customer_id = customer.id

        # Créer la commande
        order = Order(
            order_number=generate_order_number(),
            customer_id=customer_id,
            total_amount=Decimal(str(data.get('total_amount', 0))),
            tax_amount=Decimal(str(data.get('tax_amount', 0))),
            discount_amount=Decimal(str(data.get('discount_amount', 0))),
            notes=data.get('notes', '')
        )

        db.session.add(order)
        db.session.flush()

        # Ajouter les articles
        for item_data in data.get('items', []):
            product = Product.query.get(item_data['product_id'])
            if not product or product.stock_quantity < item_data['quantity']:
                raise Exception(f"Stock insuffisant pour {product.name if product else 'produit inconnu'}")

            order_item = OrderItem(
                order_id=order.id,
                product_id=item_data['product_id'],
                quantity=item_data['quantity'],
                unit_price=Decimal(str(item_data['unit_price'])),
                total_price=Decimal(str(item_data['total_price']))
            )
            db.session.add(order_item)

            # Réduire le stock
            product.stock_quantity -= item_data['quantity']

            # Enregistrer le mouvement de stock
            movement = StockMovement(
                product_id=product.id,
                movement_type='out',
                quantity=item_data['quantity'],
                reference=f"order_{order.order_number}",
                notes=f"Vente - Commande {order.order_number}"
            )
            db.session.add(movement)

        db.session.commit()

        return jsonify({
            'success': True,
            'order_id': order.id,
            'order_number': order.order_number,
            'message': 'Commande créée avec succès!'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la création de la commande: {str(e)}'
        }), 400

@app.route('/orders')
def orders():
    """Page de gestion des commandes"""
    status = request.args.get('status', '')
    search = request.args.get('search', '')

    query = Order.query

    if status:
        query = query.filter_by(status=status)

    if search:
        query = query.join(Customer).filter(
            db.or_(
                Order.order_number.contains(search),
                Customer.name.contains(search)
            )
        )

    orders = query.order_by(Order.created_at.desc()).all()

    return render_template('orders.html', orders=orders)

@app.route('/orders/<int:order_id>')
def order_detail(order_id):
    """Détail d'une commande"""
    order = Order.query.get_or_404(order_id)
    return render_template('order_detail.html', order=order)

@app.route('/orders/<int:order_id>/payment', methods=['POST'])
def add_payment(order_id):
    """Ajouter un paiement à une commande"""
    try:
        order = Order.query.get_or_404(order_id)

        amount = Decimal(request.form.get('amount', 0))
        payment_method = request.form.get('payment_method', 'cash')
        reference = request.form.get('reference', '')
        notes = request.form.get('notes', '')

        if amount <= 0:
            flash('Le montant doit être supérieur à 0', 'error')
            return redirect(url_for('order_detail', order_id=order_id))

        if amount > order.remaining_amount:
            flash('Le montant ne peut pas dépasser le montant restant', 'error')
            return redirect(url_for('order_detail', order_id=order_id))

        # Créer le paiement
        payment = Payment(
            order_id=order_id,
            amount=amount,
            payment_method=payment_method,
            reference=reference,
            notes=notes
        )

        db.session.add(payment)

        # Mettre à jour le montant payé
        order.paid_amount += amount

        # Mettre à jour le statut
        if order.is_fully_paid:
            order.payment_status = 'paid'
            order.status = 'completed'
        else:
            order.payment_status = 'partial'

        order.updated_at = datetime.utcnow()

        db.session.commit()
        flash('Paiement ajouté avec succès!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout du paiement: {str(e)}', 'error')

    return redirect(url_for('order_detail', order_id=order_id))

@app.route('/orders/<int:order_id>/invoice')
def generate_invoice(order_id):
    """Générer et télécharger la facture PDF"""
    try:
        order = Order.query.get_or_404(order_id)

        # Récupérer les informations de l'entreprise
        company_info = {
            'name': get_setting('company_name', 'Magasin de Meubles Premium'),
            'address': get_setting('company_address', '123 Rue des Meubles, Ville, Pays'),
            'phone': get_setting('company_phone', '+213 XX XX XX XX XX'),
            'email': get_setting('company_email', '<EMAIL>')
        }

        pdf_generator = PDFGenerator(company_info)
        filename = pdf_generator.generate_invoice(order)

        return send_file(filename, as_attachment=True, download_name=f"facture_{order.order_number}.pdf")

    except Exception as e:
        flash(f'Erreur lors de la génération de la facture: {str(e)}', 'error')
        return redirect(url_for('order_detail', order_id=order_id))

@app.route('/orders/<int:order_id>/slip')
def generate_order_slip(order_id):
    """Générer et télécharger le bon de commande PDF"""
    try:
        order = Order.query.get_or_404(order_id)

        company_info = {
            'name': get_setting('company_name', 'Magasin de Meubles Premium'),
            'address': get_setting('company_address', '123 Rue des Meubles, Ville, Pays'),
            'phone': get_setting('company_phone', '+213 XX XX XX XX XX'),
            'email': get_setting('company_email', '<EMAIL>')
        }

        pdf_generator = PDFGenerator(company_info)
        filename = pdf_generator.generate_order_slip(order)

        return send_file(filename, as_attachment=True, download_name=f"bon_commande_{order.order_number}.pdf")

    except Exception as e:
        flash(f'Erreur lors de la génération du bon: {str(e)}', 'error')
        return redirect(url_for('order_detail', order_id=order_id))

@app.route('/reports')
def reports():
    """Page des rapports et statistiques"""
    # Statistiques générales
    from sqlalchemy import extract, func

    # Ventes du mois actuel
    current_month_sales = db.session.query(func.sum(Payment.amount)).filter(
        extract('month', Payment.created_at) == datetime.now().month,
        extract('year', Payment.created_at) == datetime.now().year
    ).scalar() or 0

    # Ventes du mois précédent
    prev_month = datetime.now().month - 1 if datetime.now().month > 1 else 12
    prev_year = datetime.now().year if datetime.now().month > 1 else datetime.now().year - 1

    prev_month_sales = db.session.query(func.sum(Payment.amount)).filter(
        extract('month', Payment.created_at) == prev_month,
        extract('year', Payment.created_at) == prev_year
    ).scalar() or 0

    # Produits les plus vendus
    top_products = db.session.query(
        Product.name,
        func.sum(OrderItem.quantity).label('total_sold')
    ).join(OrderItem).group_by(Product.id).order_by(
        func.sum(OrderItem.quantity).desc()
    ).limit(10).all()

    # Commandes en attente
    pending_orders = Order.query.filter_by(payment_status='unpaid').count()
    partial_orders = Order.query.filter_by(payment_status='partial').count()

    return render_template('reports.html',
                         current_month_sales=current_month_sales,
                         prev_month_sales=prev_month_sales,
                         top_products=top_products,
                         pending_orders=pending_orders,
                         partial_orders=partial_orders)

@app.route('/expenses')
def expenses():
    """Page de gestion des frais"""
    expenses = Expense.query.order_by(Expense.date.desc()).all()

    # Calculer le total des frais du mois
    from sqlalchemy import extract, func
    monthly_expenses = db.session.query(func.sum(Expense.amount)).filter(
        extract('month', Expense.date) == datetime.now().month,
        extract('year', Expense.date) == datetime.now().year
    ).scalar() or 0

    return render_template('expenses.html', expenses=expenses, monthly_expenses=monthly_expenses)

@app.route('/expenses/add', methods=['GET', 'POST'])
def add_expense():
    """Ajouter un nouveau frais"""
    if request.method == 'POST':
        try:
            expense = Expense(
                name=request.form.get('name'),
                amount=Decimal(request.form.get('amount', 0)),
                category=request.form.get('category'),
                description=request.form.get('description'),
                date=datetime.strptime(request.form.get('date'), '%Y-%m-%d').date(),
                is_recurring=bool(request.form.get('is_recurring'))
            )

            db.session.add(expense)
            db.session.commit()
            flash('Frais ajouté avec succès!', 'success')
            return redirect(url_for('expenses'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout du frais: {str(e)}', 'error')

    return render_template('add_expense.html')

# Fonctions utilitaires
def generate_order_number():
    prefix = "CMD"
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{prefix}{timestamp}"

def get_setting(key, default_value):
    """Récupérer une valeur de paramètre"""
    setting = Settings.query.filter_by(key=key).first()
    return setting.value if setting else default_value

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(host='0.0.0.0', port=5000, debug=True)
