{% extends "base.html" %}

{% block title %}Commande {{ order.order_number }} - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-file-invoice me-2"></i>
        Commande {{ order.order_number }}
    </h1>
    <div class="btn-group">
        <a href="{{ url_for('orders') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Retour
        </a>
        <a href="{{ url_for('generate_invoice', order_id=order.id) }}" class="btn btn-success">
            <i class="fas fa-file-pdf me-2"></i>
            Facture PDF
        </a>
        <a href="{{ url_for('generate_order_slip', order_id=order.id) }}" class="btn btn-info">
            <i class="fas fa-file-alt me-2"></i>
            Bon de commande
        </a>
    </div>
</div>

<div class="row">
    <!-- Informations de la commande -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informations de la commande
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Numéro:</strong></td>
                                <td>{{ order.order_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date:</strong></td>
                                <td>{{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>
                                    {% if order.status == 'completed' %}
                                        <span class="badge bg-success">Terminé</span>
                                    {% elif order.status == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-secondary">Annulé</span>
                                    {% else %}
                                        <span class="badge bg-info">{{ order.status.title() }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Paiement:</strong></td>
                                <td>
                                    {% if order.payment_status == 'paid' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            Payé intégralement
                                        </span>
                                    {% elif order.payment_status == 'partial' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            Partiellement payé
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>
                                            Non payé
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Client</h6>
                        {% if order.customer %}
                        <div class="border rounded p-3">
                            <h6 class="mb-1">{{ order.customer.name }}</h6>
                            {% if order.customer.phone %}
                            <p class="mb-1">
                                <i class="fas fa-phone me-1"></i>
                                {{ order.customer.phone }}
                            </p>
                            {% endif %}
                            {% if order.customer.email %}
                            <p class="mb-1">
                                <i class="fas fa-envelope me-1"></i>
                                {{ order.customer.email }}
                            </p>
                            {% endif %}
                            {% if order.customer.address %}
                            <p class="mb-0">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ order.customer.address }}
                            </p>
                            {% endif %}
                        </div>
                        {% else %}
                        <div class="border rounded p-3 text-muted">
                            <i class="fas fa-user-slash me-2"></i>
                            Client anonyme
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                {% if order.notes %}
                <div class="mt-3">
                    <h6>Notes</h6>
                    <div class="alert alert-info">
                        <i class="fas fa-sticky-note me-2"></i>
                        {{ order.notes }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Articles commandés -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-basket me-2"></i>
                    Articles commandés ({{ order.items.count() }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Prix unitaire</th>
                                <th>Quantité</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order.items %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ item.product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.product.code }}</small>
                                    </div>
                                </td>
                                <td>{{ "{:,.0f}".format(item.unit_price) }} DA</td>
                                <td>
                                    <span class="badge bg-primary">{{ item.quantity }}</span>
                                </td>
                                <td>
                                    <strong>{{ "{:,.0f}".format(item.total_price) }} DA</strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <td colspan="3"><strong>Sous-total:</strong></td>
                                <td><strong>{{ "{:,.0f}".format(order.items|sum(attribute='total_price')) }} DA</strong></td>
                            </tr>
                            {% if order.discount_amount > 0 %}
                            <tr class="table-light">
                                <td colspan="3"><strong>Remise:</strong></td>
                                <td><strong class="text-success">-{{ "{:,.0f}".format(order.discount_amount) }} DA</strong></td>
                            </tr>
                            {% endif %}
                            {% if order.tax_amount > 0 %}
                            <tr class="table-light">
                                <td colspan="3"><strong>TVA:</strong></td>
                                <td><strong>{{ "{:,.0f}".format(order.tax_amount) }} DA</strong></td>
                            </tr>
                            {% endif %}
                            <tr class="table-primary">
                                <td colspan="3"><strong>TOTAL:</strong></td>
                                <td><strong class="fs-5">{{ "{:,.0f}".format(order.total_amount) }} DA</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Historique des paiements -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Historique des paiements
                    </h5>
                    {% if order.payment_status != 'paid' %}
                    <button class="btn btn-sm btn-success" onclick="showPaymentModal()">
                        <i class="fas fa-plus me-1"></i>
                        Ajouter paiement
                    </button>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                {% if order.payments.count() > 0 %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Montant</th>
                                <th>Méthode</th>
                                <th>Référence</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in order.payments.order_by(order.payments.created_at.desc()) %}
                            <tr>
                                <td>{{ payment.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                <td>
                                    <strong class="text-success">{{ "{:,.0f}".format(payment.amount) }} DA</strong>
                                </td>
                                <td>
                                    {% if payment.payment_method == 'cash' %}
                                        <span class="badge bg-success">Espèces</span>
                                    {% elif payment.payment_method == 'card' %}
                                        <span class="badge bg-primary">Carte</span>
                                    {% elif payment.payment_method == 'transfer' %}
                                        <span class="badge bg-info">Virement</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ payment.payment_method.title() }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.reference or '-' }}</td>
                                <td>{{ payment.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Aucun paiement enregistré</p>
                    {% if order.payment_status != 'paid' %}
                    <button class="btn btn-success" onclick="showPaymentModal()">
                        <i class="fas fa-plus me-2"></i>
                        Ajouter le premier paiement
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Résumé financier -->
    <div class="col-lg-4">
        <div class="card sticky-top">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    Résumé financier
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Montant total:</span>
                    <strong>{{ "{:,.0f}".format(order.total_amount) }} DA</strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Montant payé:</span>
                    <strong class="text-success">{{ "{:,.0f}".format(order.paid_amount) }} DA</strong>
                </div>
                <hr>
                <div class="d-flex justify-content-between mb-3">
                    <span><strong>Reste à payer:</strong></span>
                    <strong class="{{ 'text-success' if order.remaining_amount == 0 else 'text-danger' }}">
                        {{ "{:,.0f}".format(order.remaining_amount) }} DA
                    </strong>
                </div>

                {% if order.remaining_amount > 0 %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Paiement incomplet</strong>
                    <br>
                    Il reste {{ "{:,.0f}".format(order.remaining_amount) }} DA à payer
                </div>
                {% else %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Commande entièrement payée</strong>
                </div>
                {% endif %}

                <!-- Actions rapides -->
                <div class="d-grid gap-2">
                    {% if order.payment_status != 'paid' %}
                    <button class="btn btn-success" onclick="showPaymentModal()">
                        <i class="fas fa-money-bill me-2"></i>
                        Ajouter paiement
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('generate_invoice', order_id=order.id) }}" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>
                        Télécharger facture
                    </a>
                    
                    <a href="{{ url_for('generate_order_slip', order_id=order.id) }}" class="btn btn-info">
                        <i class="fas fa-file-alt me-2"></i>
                        Bon de commande
                    </a>
                    
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-money-bill me-2"></i>
                    Ajouter un paiement
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_payment', order_id=order.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Montant restant à payer</label>
                        <input type="text" class="form-control" value="{{ '{:,.0f}'.format(order.remaining_amount) }} DA" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Montant à payer</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="amount" step="0.01" min="0" max="{{ order.remaining_amount }}" value="{{ order.remaining_amount }}" required>
                            <span class="input-group-text">DA</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Méthode de paiement</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="cash">Espèces</option>
                            <option value="card">Carte bancaire</option>
                            <option value="transfer">Virement</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Référence (optionnel)</label>
                        <input type="text" class="form-control" name="reference" placeholder="Numéro de transaction, etc.">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" name="notes" rows="2" placeholder="Notes sur le paiement..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        Enregistrer le paiement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showPaymentModal() {
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

// Validation du formulaire de paiement
document.querySelector('#paymentModal form').addEventListener('submit', function(e) {
    const amount = parseFloat(this.amount.value) || 0;
    const maxAmount = {{ order.remaining_amount }};
    
    if (amount <= 0) {
        alert('Le montant doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (amount > maxAmount) {
        alert('Le montant ne peut pas dépasser le montant restant');
        e.preventDefault();
        return;
    }
});

// Styles d'impression
const printStyles = `
    @media print {
        .btn, .modal, .navbar, .sidebar { display: none !important; }
        .main-content { margin-left: 0 !important; }
        .card { border: 1px solid #000 !important; box-shadow: none !important; }
        .table { font-size: 12px; }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
