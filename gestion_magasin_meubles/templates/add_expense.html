{% extends "base.html" %}

{% block title %}Ajouter une charge - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus me-2"></i>
        Ajouter une charge
    </h1>
    <a href="{{ url_for('expenses') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Retour à la liste
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    Informations de la charge
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Date -->
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">Date de la charge <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ date.today() }}" required>
                        </div>

                        <!-- Montant -->
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">Montant (DA) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                                <span class="input-group-text">DA</span>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="description" name="description" 
                               placeholder="Description de la charge..." required>
                    </div>

                    <div class="row">
                        <!-- Catégorie -->
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">Catégorie <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required onchange="updateCategoryInfo()">
                                <option value="">Sélectionner une catégorie</option>
                                <option value="rent">🏠 Loyer</option>
                                <option value="utilities">⚡ Services publics (électricité, eau, gaz)</option>
                                <option value="supplies">📦 Fournitures de bureau</option>
                                <option value="marketing">📢 Marketing et publicité</option>
                                <option value="maintenance">🔧 Maintenance et réparations</option>
                                <option value="insurance">🛡️ Assurance</option>
                                <option value="transport">🚚 Transport et livraison</option>
                                <option value="other">📋 Autres</option>
                            </select>
                        </div>

                        <!-- Type -->
                        <div class="col-md-6 mb-3">
                            <label for="expense_type" class="form-label">Type de charge <span class="text-danger">*</span></label>
                            <select class="form-select" id="expense_type" name="expense_type" required onchange="updateTypeInfo()">
                                <option value="">Sélectionner le type</option>
                                <option value="fixed">⚓ Charge fixe</option>
                                <option value="variable">📈 Charge variable</option>
                            </select>
                        </div>
                    </div>

                    <!-- Informations sur le type -->
                    <div class="mb-3" id="typeInfo" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="typeDescription"></span>
                        </div>
                    </div>

                    <!-- Fournisseur -->
                    <div class="mb-3">
                        <label for="supplier" class="form-label">Fournisseur / Prestataire</label>
                        <input type="text" class="form-control" id="supplier" name="supplier" 
                               placeholder="Nom du fournisseur ou prestataire">
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes complémentaires</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Notes, références, détails supplémentaires..."></textarea>
                    </div>

                    <!-- Récurrence (pour les charges fixes) -->
                    <div class="mb-3" id="recurrenceSection" style="display: none;">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring">
                            <label class="form-check-label" for="is_recurring">
                                <strong>Charge récurrente</strong>
                                <br>
                                <small class="text-muted">Cette charge se répète chaque mois (ex: loyer, assurance)</small>
                            </label>
                        </div>
                    </div>

                    <!-- Aperçu de l'impact -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-calculator me-2"></i>
                                    Impact sur la rentabilité
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <small class="text-muted">Charges mensuelles actuelles:</small>
                                        <div><strong>{{ "{:,.0f}".format(current_monthly_expenses) }} DA</strong></div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Nouvelle charge:</small>
                                        <div><strong id="newExpenseAmount">0 DA</strong></div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Total après ajout:</small>
                                        <div><strong id="totalAfterExpense">{{ "{:,.0f}".format(current_monthly_expenses) }} DA</strong></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('expenses') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Enregistrer la charge
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Mettre à jour les informations de catégorie
function updateCategoryInfo() {
    const category = document.getElementById('category').value;
    const typeSelect = document.getElementById('expense_type');
    
    // Suggestions de type selon la catégorie
    if (category === 'rent' || category === 'insurance') {
        typeSelect.value = 'fixed';
        updateTypeInfo();
    } else if (category === 'supplies' || category === 'marketing' || category === 'transport') {
        typeSelect.value = 'variable';
        updateTypeInfo();
    }
}

// Mettre à jour les informations de type
function updateTypeInfo() {
    const type = document.getElementById('expense_type').value;
    const typeInfo = document.getElementById('typeInfo');
    const typeDescription = document.getElementById('typeDescription');
    const recurrenceSection = document.getElementById('recurrenceSection');
    
    if (type === 'fixed') {
        typeDescription.textContent = 'Charge fixe : Montant constant chaque mois (loyer, assurance, abonnements...)';
        typeInfo.className = 'alert alert-info';
        recurrenceSection.style.display = 'block';
    } else if (type === 'variable') {
        typeDescription.textContent = 'Charge variable : Montant qui varie selon l\'activité (fournitures, transport, marketing...)';
        typeInfo.className = 'alert alert-warning';
        recurrenceSection.style.display = 'none';
        document.getElementById('is_recurring').checked = false;
    }
    
    if (type) {
        typeInfo.style.display = 'block';
    } else {
        typeInfo.style.display = 'none';
        recurrenceSection.style.display = 'none';
    }
}

// Calculer l'impact financier
function calculateImpact() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const currentExpenses = {{ current_monthly_expenses }};
    
    document.getElementById('newExpenseAmount').textContent = amount.toLocaleString() + ' DA';
    document.getElementById('totalAfterExpense').textContent = (currentExpenses + amount).toLocaleString() + ' DA';
}

// Écouter les changements de montant
document.getElementById('amount').addEventListener('input', calculateImpact);

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const description = document.getElementById('description').value.trim();
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const category = document.getElementById('category').value;
    const type = document.getElementById('expense_type').value;
    
    if (!description) {
        alert('La description est obligatoire');
        e.preventDefault();
        return;
    }
    
    if (amount <= 0) {
        alert('Le montant doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (!category) {
        alert('Veuillez sélectionner une catégorie');
        e.preventDefault();
        return;
    }
    
    if (!type) {
        alert('Veuillez sélectionner le type de charge');
        e.preventDefault();
        return;
    }
    
    // Confirmation pour les gros montants
    if (amount > 50000) {
        if (!confirm(`Le montant de ${amount.toLocaleString()} DA est élevé. Confirmer ?`)) {
            e.preventDefault();
            return;
        }
    }
});

// Suggestions automatiques selon la catégorie
const categoryDescriptions = {
    'rent': 'Loyer du magasin',
    'utilities': 'Facture électricité/eau/gaz',
    'supplies': 'Fournitures de bureau',
    'marketing': 'Campagne publicitaire',
    'maintenance': 'Réparation équipement',
    'insurance': 'Prime d\'assurance',
    'transport': 'Frais de livraison',
    'other': 'Autre charge'
};

document.getElementById('category').addEventListener('change', function() {
    const description = document.getElementById('description');
    if (!description.value && categoryDescriptions[this.value]) {
        description.value = categoryDescriptions[this.value];
    }
});

// Focus sur le premier champ
document.getElementById('description').focus();

// Définir la date d'aujourd'hui par défaut
document.getElementById('date').valueAsDate = new Date();
</script>
{% endblock %}
