{% extends "base.html" %}

{% block title %}Ventes - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-shopping-cart me-2"></i>
        Point de vente
    </h1>
    <button class="btn btn-success" onclick="processOrder()">
        <i class="fas fa-check me-2"></i>
        Finaliser la vente
    </button>
</div>

<div class="row">
    <!-- Produits disponibles -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Produits disponibles
                </h5>
            </div>
            <div class="card-body">
                <!-- Filtres de recherche -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="searchProduct" placeholder="Rechercher un produit...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="categoryFilter">
                            <option value="">Toutes les catégories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="filterProducts()">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>

                <!-- Liste des produits -->
                <div class="row" id="productsList">
                    {% for product in products %}
                    <div class="col-lg-4 col-md-6 mb-3 product-item" data-name="{{ product.name.lower() }}" data-category="{{ product.category_id or '' }}">
                        <div class="card h-100 product-card" style="cursor: pointer;" onclick="addToCart({{ product.id }}, '{{ product.name }}', {{ product.sale_price }}, {{ product.stock_quantity }})">
                            <div class="card-body text-center">
                                <div class="mb-2">
                                    <i class="fas fa-couch fa-2x text-primary"></i>
                                </div>
                                <h6 class="card-title">{{ product.name }}</h6>
                                <p class="card-text">
                                    <small class="text-muted">{{ product.code }}</small>
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-primary mb-0">{{ "{:,.0f}".format(product.sale_price) }} DA</span>
                                    <span class="badge bg-{{ 'success' if product.stock_quantity > product.min_stock else 'warning' if product.stock_quantity > 0 else 'danger' }}">
                                        Stock: {{ product.stock_quantity }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Panier -->
    <div class="col-lg-4">
        <div class="card sticky-top">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-basket me-2"></i>
                    Panier (<span id="cartCount">0</span>)
                </h5>
            </div>
            <div class="card-body">
                <!-- Articles du panier -->
                <div id="cartItems" class="mb-3">
                    <div class="text-center text-muted py-4" id="emptyCart">
                        <i class="fas fa-shopping-basket fa-3x mb-3"></i>
                        <p>Panier vide</p>
                        <small>Cliquez sur un produit pour l'ajouter</small>
                    </div>
                </div>

                <!-- Résumé -->
                <div id="cartSummary" style="display: none;">
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Sous-total:</span>
                        <span id="subtotal">0 DA</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Remise:</span>
                        <div class="input-group input-group-sm" style="width: 100px;">
                            <input type="number" class="form-control" id="discount" value="0" min="0" onchange="updateTotal()">
                            <span class="input-group-text">DA</span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>TVA (19%):</span>
                        <span id="tax">0 DA</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total:</strong>
                        <strong id="total" class="text-primary">0 DA</strong>
                    </div>

                    <!-- Informations client -->
                    <div class="mb-3">
                        <label class="form-label">Client</label>
                        <select class="form-select" id="customerId">
                            <option value="">Client anonyme</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}">{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Nouveau client -->
                    <div id="newCustomerForm" style="display: none;">
                        <div class="mb-2">
                            <input type="text" class="form-control form-control-sm" id="customerName" placeholder="Nom du client">
                        </div>
                        <div class="mb-2">
                            <input type="tel" class="form-control form-control-sm" id="customerPhone" placeholder="Téléphone">
                        </div>
                        <div class="mb-2">
                            <textarea class="form-control form-control-sm" id="customerAddress" placeholder="Adresse" rows="2"></textarea>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button class="btn btn-outline-secondary btn-sm w-100" onclick="toggleNewCustomer()">
                            <i class="fas fa-user-plus me-1"></i>
                            Nouveau client
                        </button>
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" id="orderNotes" rows="2" placeholder="Notes sur la commande..."></textarea>
                    </div>

                    <!-- Actions -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="processOrder()">
                            <i class="fas fa-check me-2"></i>
                            Finaliser la vente
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearCart()">
                            <i class="fas fa-trash me-2"></i>
                            Vider le panier
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>
                    Paiement
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Montant total</label>
                    <input type="text" class="form-control" id="paymentTotal" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Montant payé</label>
                    <input type="number" class="form-control" id="paidAmount" step="0.01" min="0">
                </div>
                <div class="mb-3">
                    <label class="form-label">Méthode de paiement</label>
                    <select class="form-select" id="paymentMethod">
                        <option value="cash">Espèces</option>
                        <option value="card">Carte bancaire</option>
                        <option value="transfer">Virement</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Référence (optionnel)</label>
                    <input type="text" class="form-control" id="paymentReference" placeholder="Numéro de transaction, etc.">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="confirmPayment()">
                    <i class="fas fa-check me-2"></i>
                    Confirmer le paiement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let cart = [];
let orderTotal = 0;

// Ajouter un produit au panier
function addToCart(productId, productName, price, stock) {
    if (stock <= 0) {
        alert('Produit en rupture de stock');
        return;
    }
    
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        if (existingItem.quantity >= stock) {
            alert('Stock insuffisant');
            return;
        }
        existingItem.quantity++;
    } else {
        cart.push({
            id: productId,
            name: productName,
            price: price,
            quantity: 1,
            stock: stock
        });
    }
    
    updateCartDisplay();
}

// Mettre à jour l'affichage du panier
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const emptyCart = document.getElementById('emptyCart');
    const cartSummary = document.getElementById('cartSummary');
    const cartCount = document.getElementById('cartCount');
    
    if (cart.length === 0) {
        emptyCart.style.display = 'block';
        cartSummary.style.display = 'none';
        cartCount.textContent = '0';
        return;
    }
    
    emptyCart.style.display = 'none';
    cartSummary.style.display = 'block';
    cartCount.textContent = cart.reduce((sum, item) => sum + item.quantity, 0);
    
    let html = '';
    cart.forEach((item, index) => {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div class="flex-grow-1">
                    <small class="fw-bold">${item.name}</small>
                    <br>
                    <small class="text-muted">${item.price.toLocaleString()} DA × ${item.quantity}</small>
                </div>
                <div class="d-flex align-items-center">
                    <button class="btn btn-sm btn-outline-secondary me-1" onclick="changeQuantity(${index}, -1)">-</button>
                    <span class="mx-2">${item.quantity}</span>
                    <button class="btn btn-sm btn-outline-secondary me-2" onclick="changeQuantity(${index}, 1)">+</button>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    
    cartItems.innerHTML = html;
    updateTotal();
}

// Changer la quantité d'un article
function changeQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;
    
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }
    
    if (newQuantity > item.stock) {
        alert('Stock insuffisant');
        return;
    }
    
    item.quantity = newQuantity;
    updateCartDisplay();
}

// Supprimer un article du panier
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
}

// Vider le panier
function clearCart() {
    if (confirm('Êtes-vous sûr de vouloir vider le panier ?')) {
        cart = [];
        updateCartDisplay();
    }
}

// Mettre à jour le total
function updateTotal() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const taxRate = 0.19;
    const taxAmount = (subtotal - discount) * taxRate;
    const total = subtotal - discount + taxAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toLocaleString() + ' DA';
    document.getElementById('tax').textContent = taxAmount.toLocaleString() + ' DA';
    document.getElementById('total').textContent = total.toLocaleString() + ' DA';
    
    orderTotal = total;
}

// Basculer le formulaire nouveau client
function toggleNewCustomer() {
    const form = document.getElementById('newCustomerForm');
    const select = document.getElementById('customerId');
    
    if (form.style.display === 'none') {
        form.style.display = 'block';
        select.value = '';
        select.disabled = true;
    } else {
        form.style.display = 'none';
        select.disabled = false;
    }
}

// Traiter la commande
function processOrder() {
    if (cart.length === 0) {
        alert('Le panier est vide');
        return;
    }
    
    document.getElementById('paymentTotal').value = orderTotal.toLocaleString() + ' DA';
    document.getElementById('paidAmount').value = orderTotal;
    
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

// Confirmer le paiement et créer la commande
function confirmPayment() {
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const paymentMethod = document.getElementById('paymentMethod').value;
    const paymentReference = document.getElementById('paymentReference').value;
    
    if (paidAmount <= 0) {
        alert('Veuillez saisir un montant valide');
        return;
    }
    
    // Préparer les données de la commande
    const orderData = {
        customer_id: document.getElementById('customerId').value || null,
        customer_name: document.getElementById('customerName').value || null,
        customer_phone: document.getElementById('customerPhone').value || null,
        customer_address: document.getElementById('customerAddress').value || null,
        items: cart.map(item => ({
            product_id: item.id,
            quantity: item.quantity,
            unit_price: item.price,
            total_price: item.price * item.quantity
        })),
        total_amount: orderTotal,
        tax_amount: (cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - (parseFloat(document.getElementById('discount').value) || 0)) * 0.19,
        discount_amount: parseFloat(document.getElementById('discount').value) || 0,
        notes: document.getElementById('orderNotes').value || '',
        payment: {
            amount: paidAmount,
            method: paymentMethod,
            reference: paymentReference
        }
    };
    
    // Envoyer la commande
    fetch('/orders/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Commande créée avec succès !');
            
            // Ajouter le paiement si nécessaire
            if (paidAmount > 0) {
                const paymentForm = new FormData();
                paymentForm.append('amount', paidAmount);
                paymentForm.append('payment_method', paymentMethod);
                paymentForm.append('reference', paymentReference);
                
                fetch(`/orders/${data.order_id}/payment`, {
                    method: 'POST',
                    body: paymentForm
                })
                .then(() => {
                    // Rediriger vers la facture
                    window.open(`/orders/${data.order_id}/invoice`, '_blank');
                    
                    // Réinitialiser le panier
                    cart = [];
                    updateCartDisplay();
                    
                    // Fermer le modal
                    bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
                });
            }
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de la création de la commande');
    });
}

// Filtrer les produits
function filterProducts() {
    const search = document.getElementById('searchProduct').value.toLowerCase();
    const category = document.getElementById('categoryFilter').value;
    const products = document.querySelectorAll('.product-item');
    
    products.forEach(product => {
        const name = product.dataset.name;
        const productCategory = product.dataset.category;
        
        const matchesSearch = !search || name.includes(search);
        const matchesCategory = !category || productCategory === category;
        
        if (matchesSearch && matchesCategory) {
            product.style.display = 'block';
        } else {
            product.style.display = 'none';
        }
    });
}

// Recherche en temps réel
document.getElementById('searchProduct').addEventListener('input', filterProducts);
document.getElementById('categoryFilter').addEventListener('change', filterProducts);
</script>
{% endblock %}
