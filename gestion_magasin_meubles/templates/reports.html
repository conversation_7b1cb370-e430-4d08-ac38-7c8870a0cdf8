{% extends "base.html" %}

{% block title %}Rapports financiers - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-line me-2"></i>
        Rapports financiers
    </h1>
    <div class="btn-group">
        <a href="{{ url_for('expenses') }}" class="btn btn-outline-primary">
            <i class="fas fa-receipt me-2"></i>
            Gérer les charges
        </a>
        <button class="btn btn-success" onclick="exportReport()">
            <i class="fas fa-download me-2"></i>
            Exporter PDF
        </button>
    </div>
</div>

<!-- Filtres de période -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Date de début</label>
                <input type="date" class="form-control" name="start_date" value="{{ request.args.get('start_date', '') }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">Date de fin</label>
                <input type="date" class="form-control" name="end_date" value="{{ request.args.get('end_date', '') }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">Période prédéfinie</label>
                <select class="form-select" name="period" onchange="setPredefinedPeriod(this.value)">
                    <option value="">Personnalisée</option>
                    <option value="today">Aujourd'hui</option>
                    <option value="week">Cette semaine</option>
                    <option value="month">Ce mois</option>
                    <option value="quarter">Ce trimestre</option>
                    <option value="year">Cette année</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>
                    Générer rapport
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Indicateurs clés -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(total_revenue) }} DA</h4>
                    <p class="mb-0">Chiffre d'affaires</p>
                </div>
                <i class="fas fa-money-bill-wave fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(total_cost) }} DA</h4>
                    <p class="mb-0">Coût des ventes</p>
                </div>
                <i class="fas fa-shopping-cart fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(total_expenses) }} DA</h4>
                    <p class="mb-0">Charges</p>
                </div>
                <i class="fas fa-receipt fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card {{ 'success' if net_profit > 0 else 'danger' }}">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(net_profit) }} DA</h4>
                    <p class="mb-0">Bénéfice net</p>
                </div>
                <i class="fas fa-chart-line fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Analyse des ventes -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Analyse des ventes
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-primary">{{ orders_count }}</h3>
                            <p class="mb-0">Commandes</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-success">{{ items_sold }}</h3>
                            <p class="mb-0">Articles vendus</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-info">{{ "{:,.0f}".format(average_order) }} DA</h3>
                            <p class="mb-0">Panier moyen</p>
                        </div>
                    </div>
                </div>

                <!-- Graphique des ventes (placeholder) -->
                <div class="chart-container" style="height: 300px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <div class="text-center">
                        <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Graphique des ventes</h5>
                        <p class="text-muted">Évolution du chiffre d'affaires sur la période</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top produits -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    Top 10 des produits
                </h5>
            </div>
            <div class="card-body">
                {% if top_products %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Rang</th>
                                <th>Produit</th>
                                <th>Quantité vendue</th>
                                <th>CA généré</th>
                                <th>Marge</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in top_products %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">#{{ loop.index }}</span>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ product.code }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ product.quantity_sold }}</span>
                                </td>
                                <td>
                                    <strong>{{ "{:,.0f}".format(product.revenue) }} DA</strong>
                                </td>
                                <td>
                                    <span class="text-{{ 'success' if product.margin > 20 else 'warning' if product.margin > 10 else 'danger' }}">
                                        {{ "{:.1f}".format(product.margin) }}%
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Aucune vente sur cette période</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Résumé financier détaillé -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    Résumé financier
                </h5>
            </div>
            <div class="card-body">
                <div class="financial-summary">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Chiffre d'affaires:</span>
                        <strong class="text-success">{{ "{:,.0f}".format(total_revenue) }} DA</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Coût des ventes:</span>
                        <span class="text-danger">{{ "{:,.0f}".format(total_cost) }} DA</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span><strong>Marge brute:</strong></span>
                        <strong class="text-info">{{ "{:,.0f}".format(gross_profit) }} DA</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Taux de marge:</span>
                        <span class="text-info">{{ "{:.1f}".format(gross_margin) }}%</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Charges fixes:</span>
                        <span class="text-warning">{{ "{:,.0f}".format(fixed_expenses) }} DA</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Charges variables:</span>
                        <span class="text-warning">{{ "{:,.0f}".format(variable_expenses) }} DA</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <span><strong>Bénéfice net:</strong></span>
                        <strong class="{{ 'text-success' if net_profit > 0 else 'text-danger' }}">
                            {{ "{:,.0f}".format(net_profit) }} DA
                        </strong>
                    </div>
                    
                    {% if net_profit > 0 %}
                    <div class="alert alert-success">
                        <i class="fas fa-thumbs-up me-2"></i>
                        <strong>Rentabilité: {{ "{:.1f}".format(profitability) }}%</strong>
                    </div>
                    {% else %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Perte: {{ "{:.1f}".format(abs(profitability)) }}%</strong>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Répartition des charges -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-pie-chart me-2"></i>
                    Répartition des charges
                </h5>
            </div>
            <div class="card-body">
                {% if expense_categories %}
                {% for category in expense_categories %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>{{ category.name }}</span>
                        <strong>{{ "{:,.0f}".format(category.amount) }} DA</strong>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar" style="width: {{ (category.amount / total_expenses * 100) if total_expenses > 0 else 0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ "{:.1f}".format((category.amount / total_expenses * 100) if total_expenses > 0 else 0) }}% du total</small>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Aucune charge enregistrée</p>
                    <a href="{{ url_for('expenses') }}" class="btn btn-sm btn-outline-primary">
                        Ajouter des charges
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('expenses') }}" class="btn btn-outline-primary">
                        <i class="fas fa-receipt me-2"></i>
                        Gérer les charges
                    </a>
                    <button class="btn btn-outline-success" onclick="exportReport()">
                        <i class="fas fa-file-excel me-2"></i>
                        Exporter Excel
                    </button>
                    <button class="btn btn-outline-info" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        Imprimer rapport
                    </button>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Retour au tableau de bord
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function setPredefinedPeriod(period) {
    const today = new Date();
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    
    let startDate, endDate;
    
    switch(period) {
        case 'today':
            startDate = endDate = today;
            break;
        case 'week':
            startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
            endDate = today;
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = today;
            break;
        case 'quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            startDate = new Date(today.getFullYear(), quarter * 3, 1);
            endDate = today;
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = today;
            break;
        default:
            return;
    }
    
    startDateInput.value = startDate.toISOString().split('T')[0];
    endDateInput.value = endDate.toISOString().split('T')[0];
}

function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

// Styles d'impression
const printStyles = `
    @media print {
        .btn, .navbar, .sidebar { display: none !important; }
        .main-content { margin-left: 0 !important; }
        .card { border: 1px solid #000 !important; box-shadow: none !important; }
        .stats-card { border: 1px solid #000 !important; }
        .chart-container { border: 1px solid #000 !important; }
        body { font-size: 12px; }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = printStyles;
document.head.appendChild(styleSheet);

// Animation des statistiques
document.addEventListener('DOMContentLoaded', function() {
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.animation = 'fadeInUp 0.6s ease-out forwards';
    });
});

// CSS pour l'animation
const animationStyles = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;

const animationStyleSheet = document.createElement('style');
animationStyleSheet.textContent = animationStyles;
document.head.appendChild(animationStyleSheet);
</script>
{% endblock %}
