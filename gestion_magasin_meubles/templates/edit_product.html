{% extends "base.html" %}

{% block title %}Modifier {{ product.name }} - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2"></i>
        Modifier le produit
    </h1>
    <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Retour à la liste
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    {{ product.name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Code produit -->
                        <div class="col-md-6 mb-3">
                            <label for="code" class="form-label">Code produit</label>
                            <input type="text" class="form-control" id="code" name="code" value="{{ product.code }}" readonly>
                            <div class="form-text">Le code produit ne peut pas être modifié</div>
                        </div>

                        <!-- Nom -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nom du produit <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ product.description or '' }}</textarea>
                    </div>

                    <!-- Catégorie -->
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Catégorie</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">Aucune catégorie</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {{ 'selected' if product.category_id == category.id }}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="row">
                        <!-- Prix d'achat -->
                        <div class="col-md-6 mb-3">
                            <label for="purchase_price" class="form-label">Prix d'achat (DA) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="purchase_price" name="purchase_price" 
                                       step="0.01" min="0" value="{{ product.purchase_price }}" required onchange="calculateMargin()">
                                <span class="input-group-text">DA</span>
                            </div>
                        </div>

                        <!-- Prix de vente -->
                        <div class="col-md-6 mb-3">
                            <label for="sale_price" class="form-label">Prix de vente (DA) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="sale_price" name="sale_price" 
                                       step="0.01" min="0" value="{{ product.sale_price }}" required onchange="calculateMargin()">
                                <span class="input-group-text">DA</span>
                            </div>
                        </div>
                    </div>

                    <!-- Marge calculée -->
                    <div class="mb-3">
                        <div class="alert alert-info" id="marginInfo">
                            <i class="fas fa-calculator me-2"></i>
                            <strong>Marge bénéficiaire actuelle:</strong> <span id="marginValue">{{ "{:.1f}".format(product.profit_margin) }}%</span>
                            <br>
                            <strong>Bénéfice par unité:</strong> <span id="profitValue">{{ "{:,.0f}".format(product.sale_price - product.purchase_price) }} DA</span>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Stock actuel -->
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock actuel</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                       min="0" value="{{ product.stock_quantity }}">
                                <span class="input-group-text">unités</span>
                            </div>
                            <div class="form-text">
                                Stock précédent: {{ product.stock_quantity }} unités
                                {% if product.is_low_stock %}
                                <span class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Stock bas !
                                </span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Stock minimum -->
                        <div class="col-md-6 mb-3">
                            <label for="min_stock" class="form-label">Stock minimum (alerte)</label>
                            <input type="number" class="form-control" id="min_stock" name="min_stock" 
                                   min="0" value="{{ product.min_stock }}">
                            <div class="form-text">Seuil d'alerte pour le réapprovisionnement</div>
                        </div>
                    </div>

                    <!-- Code-barres -->
                    <div class="mb-3">
                        <label for="barcode" class="form-label">Code-barres</label>
                        <input type="text" class="form-control" id="barcode" name="barcode" value="{{ product.barcode or '' }}">
                    </div>

                    <!-- Image -->
                    <div class="mb-3">
                        <label for="image" class="form-label">Image du produit</label>
                        {% if product.image_url %}
                        <div class="mb-2">
                            <img src="{{ product.image_url }}" alt="{{ product.name }}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                            <div class="form-text">Image actuelle</div>
                        </div>
                        {% endif %}
                        <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                        <div class="form-text">Laissez vide pour conserver l'image actuelle</div>
                        
                        <!-- Aperçu de la nouvelle image -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Aperçu" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                            <div class="form-text">Nouvelle image</div>
                        </div>
                    </div>

                    <!-- Historique des mouvements de stock -->
                    {% if product.stock_movements.count() > 0 %}
                    <div class="mb-3">
                        <h6>Historique des mouvements de stock (5 derniers)</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Quantité</th>
                                        <th>Référence</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for movement in product.stock_movements.order_by(product.stock_movements.created_at.desc()).limit(5) %}
                                    <tr>
                                        <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                        <td>
                                            {% if movement.movement_type == 'in' %}
                                                <span class="badge bg-success">Entrée</span>
                                            {% elif movement.movement_type == 'out' %}
                                                <span class="badge bg-danger">Sortie</span>
                                            {% else %}
                                                <span class="badge bg-warning">Ajustement</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ movement.quantity }}</td>
                                        <td>{{ movement.reference or '-' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Annuler
                        </a>
                        <div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer les modifications
                            </button>
                            <a href="{{ url_for('sales') }}?product={{ product.id }}" class="btn btn-success">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Vendre ce produit
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculer la marge bénéficiaire
function calculateMargin() {
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const salePrice = parseFloat(document.getElementById('sale_price').value) || 0;
    
    if (purchasePrice > 0 && salePrice > 0) {
        const profit = salePrice - purchasePrice;
        const margin = (profit / purchasePrice) * 100;
        
        document.getElementById('marginValue').textContent = margin.toFixed(1) + '%';
        document.getElementById('profitValue').textContent = profit.toLocaleString() + ' DA';
        
        // Changer la couleur selon la marge
        const alertDiv = document.getElementById('marginInfo');
        if (margin < 10) {
            alertDiv.className = 'alert alert-warning';
        } else if (margin < 20) {
            alertDiv.className = 'alert alert-info';
        } else {
            alertDiv.className = 'alert alert-success';
        }
    }
}

// Aperçu de l'image
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const salePrice = parseFloat(document.getElementById('sale_price').value) || 0;
    const oldStock = {{ product.stock_quantity }};
    const newStock = parseInt(document.getElementById('stock_quantity').value) || 0;
    
    if (!name) {
        alert('Le nom du produit est obligatoire');
        e.preventDefault();
        return;
    }
    
    if (purchasePrice <= 0) {
        alert('Le prix d\'achat doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (salePrice <= 0) {
        alert('Le prix de vente doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (salePrice < purchasePrice) {
        if (!confirm('Le prix de vente est inférieur au prix d\'achat. Voulez-vous continuer ?')) {
            e.preventDefault();
            return;
        }
    }
    
    // Alerte si changement important de stock
    if (Math.abs(newStock - oldStock) > 10) {
        if (!confirm(`Le stock va changer de ${oldStock} à ${newStock} unités. Confirmer ?`)) {
            e.preventDefault();
            return;
        }
    }
});

// Calculer la marge initiale
calculateMargin();

// Focus sur le nom
document.getElementById('name').focus();
</script>
{% endblock %}
