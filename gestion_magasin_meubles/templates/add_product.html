{% extends "base.html" %}

{% block title %}Ajouter un produit - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus me-2"></i>
        Ajouter un produit
    </h1>
    <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Retour à la liste
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Informations du produit
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Code produit -->
                        <div class="col-md-6 mb-3">
                            <label for="code" class="form-label">Code produit</label>
                            <input type="text" class="form-control" id="code" name="code" placeholder="Laissez vide pour génération automatique">
                            <div class="form-text">Si vide, un code sera généré automatiquement</div>
                        </div>

                        <!-- Nom -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nom du produit <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="Description détaillée du produit..."></textarea>
                    </div>

                    <!-- Catégorie -->
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Catégorie</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">Aucune catégorie</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="row">
                        <!-- Prix d'achat -->
                        <div class="col-md-6 mb-3">
                            <label for="purchase_price" class="form-label">Prix d'achat (DA) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0" required onchange="calculateMargin()">
                                <span class="input-group-text">DA</span>
                            </div>
                        </div>

                        <!-- Prix de vente -->
                        <div class="col-md-6 mb-3">
                            <label for="sale_price" class="form-label">Prix de vente (DA) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="sale_price" name="sale_price" step="0.01" min="0" required onchange="calculateMargin()">
                                <span class="input-group-text">DA</span>
                            </div>
                        </div>
                    </div>

                    <!-- Marge calculée -->
                    <div class="mb-3">
                        <div class="alert alert-info" id="marginInfo" style="display: none;">
                            <i class="fas fa-calculator me-2"></i>
                            <strong>Marge bénéficiaire:</strong> <span id="marginValue">0%</span>
                            <br>
                            <strong>Bénéfice par unité:</strong> <span id="profitValue">0 DA</span>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Stock initial -->
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock initial</label>
                            <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" value="0">
                        </div>

                        <!-- Stock minimum -->
                        <div class="col-md-6 mb-3">
                            <label for="min_stock" class="form-label">Stock minimum (alerte)</label>
                            <input type="number" class="form-control" id="min_stock" name="min_stock" min="0" value="5">
                            <div class="form-text">Seuil d'alerte pour le réapprovisionnement</div>
                        </div>
                    </div>

                    <!-- Code-barres -->
                    <div class="mb-3">
                        <label for="barcode" class="form-label">Code-barres (optionnel)</label>
                        <input type="text" class="form-control" id="barcode" name="barcode" placeholder="Code-barres du produit">
                    </div>

                    <!-- Image -->
                    <div class="mb-3">
                        <label for="image" class="form-label">Image du produit</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                        <div class="form-text">Formats acceptés: JPG, PNG, GIF (max 5MB)</div>
                        
                        <!-- Aperçu de l'image -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Aperçu" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Enregistrer le produit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculer la marge bénéficiaire
function calculateMargin() {
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const salePrice = parseFloat(document.getElementById('sale_price').value) || 0;
    
    if (purchasePrice > 0 && salePrice > 0) {
        const profit = salePrice - purchasePrice;
        const margin = (profit / purchasePrice) * 100;
        
        document.getElementById('marginValue').textContent = margin.toFixed(1) + '%';
        document.getElementById('profitValue').textContent = profit.toLocaleString() + ' DA';
        document.getElementById('marginInfo').style.display = 'block';
        
        // Changer la couleur selon la marge
        const alertDiv = document.getElementById('marginInfo');
        if (margin < 10) {
            alertDiv.className = 'alert alert-warning';
        } else if (margin < 20) {
            alertDiv.className = 'alert alert-info';
        } else {
            alertDiv.className = 'alert alert-success';
        }
    } else {
        document.getElementById('marginInfo').style.display = 'none';
    }
}

// Aperçu de l'image
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const salePrice = parseFloat(document.getElementById('sale_price').value) || 0;
    
    if (!name) {
        alert('Le nom du produit est obligatoire');
        e.preventDefault();
        return;
    }
    
    if (purchasePrice <= 0) {
        alert('Le prix d\'achat doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (salePrice <= 0) {
        alert('Le prix de vente doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (salePrice < purchasePrice) {
        if (!confirm('Le prix de vente est inférieur au prix d\'achat. Voulez-vous continuer ?')) {
            e.preventDefault();
            return;
        }
    }
});

// Génération automatique du code produit
document.getElementById('name').addEventListener('blur', function() {
    const codeField = document.getElementById('code');
    if (!codeField.value && this.value) {
        // Générer un code basé sur le nom
        const name = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 6);
        const timestamp = Date.now().toString().slice(-4);
        codeField.value = name + timestamp;
    }
});

// Focus sur le premier champ
document.getElementById('name').focus();
</script>
{% endblock %}
