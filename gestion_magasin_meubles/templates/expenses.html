{% extends "base.html" %}

{% block title %}Gestion des charges - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-receipt me-2"></i>
        Gestion des charges
    </h1>
    <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Nouvelle charge
    </a>
</div>

<!-- Résumé des charges -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(total_expenses) }} DA</h4>
                    <p class="mb-0">Total charges</p>
                </div>
                <i class="fas fa-money-bill fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(monthly_expenses) }} DA</h4>
                    <p class="mb-0">Ce mois</p>
                </div>
                <i class="fas fa-calendar-month fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(fixed_expenses) }} DA</h4>
                    <p class="mb-0">Charges fixes</p>
                </div>
                <i class="fas fa-anchor fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(variable_expenses) }} DA</h4>
                    <p class="mb-0">Charges variables</p>
                </div>
                <i class="fas fa-chart-line fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" 
                           placeholder="Rechercher par description...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="category">
                    <option value="">Toutes les catégories</option>
                    <option value="rent" {{ 'selected' if request.args.get('category') == 'rent' }}>Loyer</option>
                    <option value="utilities" {{ 'selected' if request.args.get('category') == 'utilities' }}>Services publics</option>
                    <option value="supplies" {{ 'selected' if request.args.get('category') == 'supplies' }}>Fournitures</option>
                    <option value="marketing" {{ 'selected' if request.args.get('category') == 'marketing' }}>Marketing</option>
                    <option value="maintenance" {{ 'selected' if request.args.get('category') == 'maintenance' }}>Maintenance</option>
                    <option value="insurance" {{ 'selected' if request.args.get('category') == 'insurance' }}>Assurance</option>
                    <option value="transport" {{ 'selected' if request.args.get('category') == 'transport' }}>Transport</option>
                    <option value="other" {{ 'selected' if request.args.get('category') == 'other' }}>Autres</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="type">
                    <option value="">Tous les types</option>
                    <option value="fixed" {{ 'selected' if request.args.get('type') == 'fixed' }}>Charges fixes</option>
                    <option value="variable" {{ 'selected' if request.args.get('type') == 'variable' }}>Charges variables</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-1"></i>
                    Filtrer
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Liste des charges -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Liste des charges ({{ expenses|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if expenses %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Catégorie</th>
                        <th>Type</th>
                        <th>Montant</th>
                        <th>Fournisseur</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense in expenses %}
                    <tr>
                        <td>
                            <div>
                                {{ expense.date.strftime('%d/%m/%Y') }}
                                <br>
                                <small class="text-muted">{{ expense.created_at.strftime('%H:%M') }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ expense.description }}</strong>
                                {% if expense.notes %}
                                <br>
                                <small class="text-muted">{{ expense.notes[:50] }}{% if expense.notes|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if expense.category == 'rent' %}
                                <span class="badge bg-primary">Loyer</span>
                            {% elif expense.category == 'utilities' %}
                                <span class="badge bg-warning">Services</span>
                            {% elif expense.category == 'supplies' %}
                                <span class="badge bg-info">Fournitures</span>
                            {% elif expense.category == 'marketing' %}
                                <span class="badge bg-success">Marketing</span>
                            {% elif expense.category == 'maintenance' %}
                                <span class="badge bg-danger">Maintenance</span>
                            {% elif expense.category == 'insurance' %}
                                <span class="badge bg-secondary">Assurance</span>
                            {% elif expense.category == 'transport' %}
                                <span class="badge bg-dark">Transport</span>
                            {% else %}
                                <span class="badge bg-light text-dark">Autres</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if expense.expense_type == 'fixed' %}
                                <span class="badge bg-info">
                                    <i class="fas fa-anchor me-1"></i>
                                    Fixe
                                </span>
                            {% else %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-chart-line me-1"></i>
                                    Variable
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <strong class="text-danger">{{ "{:,.0f}".format(expense.amount) }} DA</strong>
                        </td>
                        <td>
                            {{ expense.supplier or '-' }}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('edit_expense', expense_id=expense.id) }}" class="btn btn-outline-primary" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-info" onclick="showExpenseDetails({{ expense.id }})" title="Détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <form method="POST" action="{{ url_for('delete_expense', expense_id=expense.id) }}" class="d-inline" 
                                      onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette charge ?')">
                                    <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-light">
                        <td colspan="4"><strong>Total:</strong></td>
                        <td><strong class="text-danger">{{ "{:,.0f}".format(expenses|sum(attribute='amount')) }} DA</strong></td>
                        <td colspan="2"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune charge enregistrée</h5>
            <p class="text-muted">Commencez par ajouter vos premières charges</p>
            <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Ajouter une charge
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Répartition par catégorie -->
{% if expenses %}
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-pie-chart me-2"></i>
                    Répartition par catégorie
                </h5>
            </div>
            <div class="card-body">
                {% for category_data in expense_by_category %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>{{ category_data.name }}</span>
                        <strong>{{ "{:,.0f}".format(category_data.amount) }} DA</strong>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-{{ category_data.color }}" 
                             style="width: {{ (category_data.amount / total_expenses * 100) if total_expenses > 0 else 0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ "{:.1f}".format((category_data.amount / total_expenses * 100) if total_expenses > 0 else 0) }}% du total</small>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Évolution mensuelle
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 250px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <div class="text-center">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Graphique des charges</h6>
                        <p class="text-muted">Évolution des charges par mois</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Modal détails charge -->
<div class="modal fade" id="expenseDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt me-2"></i>
                    Détails de la charge
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="expenseDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showExpenseDetails(expenseId) {
    const modal = new bootstrap.Modal(document.getElementById('expenseDetailsModal'));
    const content = document.getElementById('expenseDetailsContent');
    
    // Afficher le modal avec un spinner
    modal.show();
    
    // Simuler le chargement des détails (à remplacer par un appel AJAX réel)
    setTimeout(() => {
        // Trouver la charge dans le tableau
        const row = document.querySelector(`form[action*="${expenseId}"]`).closest('tr');
        if (row) {
            const cells = row.querySelectorAll('td');
            const date = cells[0].querySelector('div').firstChild.textContent.trim();
            const description = cells[1].querySelector('strong').textContent;
            const notes = cells[1].querySelector('small')?.textContent || 'Aucune note';
            const category = cells[2].textContent.trim();
            const type = cells[3].textContent.trim();
            const amount = cells[4].textContent.trim();
            const supplier = cells[5].textContent.trim();
            
            content.innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Date:</strong></td>
                                <td>${date}</td>
                            </tr>
                            <tr>
                                <td><strong>Description:</strong></td>
                                <td>${description}</td>
                            </tr>
                            <tr>
                                <td><strong>Catégorie:</strong></td>
                                <td>${category}</td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td>${type}</td>
                            </tr>
                            <tr>
                                <td><strong>Montant:</strong></td>
                                <td><strong class="text-danger">${amount}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Fournisseur:</strong></td>
                                <td>${supplier}</td>
                            </tr>
                            <tr>
                                <td><strong>Notes:</strong></td>
                                <td>${notes}</td>
                            </tr>
                        </table>
                        
                        <div class="d-grid gap-2 mt-3">
                            <a href="/expenses/${expenseId}/edit" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>
                                Modifier cette charge
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }
    }, 500);
}

// Animation des statistiques
document.addEventListener('DOMContentLoaded', function() {
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.animation = 'fadeInUp 0.6s ease-out forwards';
    });
});

// CSS pour l'animation
const animationStyles = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;

const animationStyleSheet = document.createElement('style');
animationStyleSheet.textContent = animationStyles;
document.head.appendChild(animationStyleSheet);

// Confirmation de suppression
document.querySelectorAll('form[action*="delete"]').forEach(form => {
    form.addEventListener('submit', function(e) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer cette charge ? Cette action est irréversible.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
