{% extends "base.html" %}

{% block title %}Tableau de bord - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2"></i>
        Tableau de bord
    </h1>
    <div class="text-muted">
        <i class="fas fa-calendar-alt me-1"></i>
        Aujourd'hui
    </div>
</div>

<!-- Statistiques principales -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ total_products }}</h3>
                    <p class="mb-0">Produits actifs</p>
                </div>
                <div class="fs-1">
                    <i class="fas fa-box"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ low_stock_products }}</h3>
                    <p class="mb-0">Stock bas</p>
                </div>
                <div class="fs-1">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ pending_orders }}</h3>
                    <p class="mb-0">Commandes en attente</p>
                </div>
                <div class="fs-1">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ "{:,.0f}".format(total_revenue_today) }} DA</h3>
                    <p class="mb-0">Ventes aujourd'hui</p>
                </div>
                <div class="fs-1">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ventes du mois -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Ventes du mois
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-primary mb-0">{{ "{:,.0f}".format(monthly_sales) }} DA</h2>
                        <p class="text-muted mb-0">Chiffre d'affaires mensuel</p>
                    </div>
                    <div class="text-end">
                        <a href="{{ url_for('reports') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-chart-bar me-1"></i>
                            Voir rapports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('sales') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Nouvelle vente
                    </a>
                    <a href="{{ url_for('add_product') }}" class="btn btn-success">
                        <i class="fas fa-box me-2"></i>
                        Ajouter produit
                    </a>
                    <a href="{{ url_for('add_expense') }}" class="btn btn-warning">
                        <i class="fas fa-money-bill me-2"></i>
                        Ajouter frais
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alertes stock bas -->
{% if low_stock_items %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Alertes stock bas
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Stock actuel</th>
                                <th>Stock minimum</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in low_stock_items %}
                            <tr>
                                <td>
                                    <strong>{{ product.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ product.code }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-danger">{{ product.stock_quantity }}</span>
                                </td>
                                <td>{{ product.min_stock }}</td>
                                <td>
                                    <a href="{{ url_for('edit_product', product_id=product.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i>
                                        Réapprovisionner
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Commandes récentes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>
                        Commandes récentes
                    </h5>
                    <a href="{{ url_for('orders') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        Voir toutes
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>N° Commande</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>
                                    <strong>{{ order.order_number }}</strong>
                                </td>
                                <td>
                                    {{ order.customer.name if order.customer else 'Client anonyme' }}
                                </td>
                                <td>
                                    <strong>{{ "{:,.0f}".format(order.total_amount) }} DA</strong>
                                </td>
                                <td>
                                    {% if order.payment_status == 'paid' %}
                                        <span class="badge bg-success">Payé</span>
                                    {% elif order.payment_status == 'partial' %}
                                        <span class="badge bg-warning">Partiel</span>
                                    {% else %}
                                        <span class="badge bg-danger">Non payé</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ order.created_at.strftime('%d/%m/%Y %H:%M') }}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('order_detail', order_id=order.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('generate_invoice', order_id=order.id) }}" class="btn btn-outline-success">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Aucune commande récente</p>
                    <a href="{{ url_for('sales') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Créer une vente
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Actualiser les données toutes les 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
    
    // Animation des cartes statistiques
    $(document).ready(function() {
        $('.stats-card').each(function(index) {
            $(this).delay(index * 100).fadeIn(500);
        });
    });
</script>
{% endblock %}
