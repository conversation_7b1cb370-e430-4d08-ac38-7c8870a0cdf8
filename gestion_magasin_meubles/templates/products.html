{% extends "base.html" %}

{% block title %}Produits - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-box me-2"></i>
        Gestion des produits
    </h1>
    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Nouveau produit
    </a>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" placeholder="Rechercher par nom, code ou description...">
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select" name="category">
                    <option value="">Toutes les catégories</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {{ 'selected' if request.args.get('category') == category.id|string }}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-1"></i>
                    Filtrer
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Liste des produits -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Liste des produits ({{ products|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Nom</th>
                        <th>Catégorie</th>
                        <th>Prix d'achat</th>
                        <th>Prix de vente</th>
                        <th>Stock</th>
                        <th>Marge</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr class="{{ 'table-warning' if product.is_low_stock }}">
                        <td>
                            <code>{{ product.code }}</code>
                        </td>
                        <td>
                            <div>
                                <strong>{{ product.name }}</strong>
                                {% if product.description %}
                                <br>
                                <small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if product.category %}
                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="text-muted">{{ "{:,.0f}".format(product.purchase_price) }} DA</span>
                        </td>
                        <td>
                            <strong>{{ "{:,.0f}".format(product.sale_price) }} DA</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-{{ 'success' if product.stock_quantity > product.min_stock else 'warning' if product.stock_quantity > 0 else 'danger' }} me-2">
                                    {{ product.stock_quantity }}
                                </span>
                                {% if product.is_low_stock %}
                                <i class="fas fa-exclamation-triangle text-warning" title="Stock bas"></i>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if product.profit_margin > 0 %}
                                <span class="text-success">+{{ "{:.1f}".format(product.profit_margin) }}%</span>
                            {% else %}
                                <span class="text-danger">{{ "{:.1f}".format(product.profit_margin) }}%</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.is_active %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactif</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('edit_product', product_id=product.id) }}" class="btn btn-outline-primary" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-info" onclick="showProductDetails({{ product.id }})" title="Détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <form method="POST" action="{{ url_for('delete_product', product_id=product.id) }}" class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')">
                                    <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun produit trouvé</h5>
            <p class="text-muted">Commencez par ajouter votre premier produit</p>
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Ajouter un produit
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal détails produit -->
<div class="modal fade" id="productDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-box me-2"></i>
                    Détails du produit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showProductDetails(productId) {
    const modal = new bootstrap.Modal(document.getElementById('productDetailsModal'));
    const content = document.getElementById('productDetailsContent');
    
    // Afficher le modal avec un spinner
    modal.show();
    
    // Simuler le chargement des détails (à remplacer par un appel AJAX réel)
    setTimeout(() => {
        // Trouver le produit dans le tableau
        const row = document.querySelector(`tr:has(form[action*="${productId}"])`);
        if (row) {
            const cells = row.querySelectorAll('td');
            const code = cells[0].textContent.trim();
            const name = cells[1].querySelector('strong').textContent;
            const description = cells[1].querySelector('small')?.textContent || 'Aucune description';
            const category = cells[2].textContent.trim();
            const purchasePrice = cells[3].textContent.trim();
            const salePrice = cells[4].textContent.trim();
            const stock = cells[5].querySelector('.badge').textContent.trim();
            const margin = cells[6].textContent.trim();
            const status = cells[7].textContent.trim();
            
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-couch fa-4x text-primary mb-3"></i>
                                <h5>${name}</h5>
                                <p class="text-muted">${description}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Code:</strong></td>
                                <td>${code}</td>
                            </tr>
                            <tr>
                                <td><strong>Catégorie:</strong></td>
                                <td>${category}</td>
                            </tr>
                            <tr>
                                <td><strong>Prix d'achat:</strong></td>
                                <td>${purchasePrice}</td>
                            </tr>
                            <tr>
                                <td><strong>Prix de vente:</strong></td>
                                <td>${salePrice}</td>
                            </tr>
                            <tr>
                                <td><strong>Stock:</strong></td>
                                <td>${stock}</td>
                            </tr>
                            <tr>
                                <td><strong>Marge:</strong></td>
                                <td>${margin}</td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>${status}</td>
                            </tr>
                        </table>
                        
                        <div class="d-grid gap-2">
                            <a href="/products/${productId}/edit" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>
                                Modifier
                            </a>
                            <button class="btn btn-success" onclick="addToSale(${productId})">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Ajouter à une vente
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    }, 500);
}

function addToSale(productId) {
    // Rediriger vers la page de vente avec le produit pré-sélectionné
    window.location.href = `/sales?product=${productId}`;
}

// Confirmation de suppression
document.querySelectorAll('form[action*="delete"]').forEach(form => {
    form.addEventListener('submit', function(e) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.')) {
            e.preventDefault();
        }
    });
});

// Mise en évidence des produits en stock bas
document.addEventListener('DOMContentLoaded', function() {
    const lowStockRows = document.querySelectorAll('.table-warning');
    lowStockRows.forEach(row => {
        row.style.animation = 'pulse 2s infinite';
    });
});

// Animation CSS pour les alertes stock bas
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { background-color: rgba(255, 193, 7, 0.1); }
        50% { background-color: rgba(255, 193, 7, 0.3); }
        100% { background-color: rgba(255, 193, 7, 0.1); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
