{% extends "base.html" %}

{% block title %}Commandes - Gestion Magasin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-file-invoice me-2"></i>
        Gestion des commandes
    </h1>
    <a href="{{ url_for('sales') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Nouvelle vente
    </a>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ orders|selectattr('payment_status', 'equalto', 'unpaid')|list|length }}</h4>
                    <p class="mb-0">Non payées</p>
                </div>
                <i class="fas fa-clock fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ orders|selectattr('payment_status', 'equalto', 'partial')|list|length }}</h4>
                    <p class="mb-0">Partiellement payées</p>
                </div>
                <i class="fas fa-exclamation-triangle fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ orders|selectattr('payment_status', 'equalto', 'paid')|list|length }}</h4>
                    <p class="mb-0">Payées</p>
                </div>
                <i class="fas fa-check-circle fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">{{ "{:,.0f}".format(orders|sum(attribute='total_amount')) }} DA</h4>
                    <p class="mb-0">Total commandes</p>
                </div>
                <i class="fas fa-money-bill-wave fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" 
                           placeholder="N° commande ou nom client...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">Tous les statuts</option>
                    <option value="pending" {{ 'selected' if request.args.get('status') == 'pending' }}>En attente</option>
                    <option value="partial" {{ 'selected' if request.args.get('status') == 'partial' }}>Partiel</option>
                    <option value="completed" {{ 'selected' if request.args.get('status') == 'completed' }}>Terminé</option>
                    <option value="cancelled" {{ 'selected' if request.args.get('status') == 'cancelled' }}>Annulé</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="payment_status">
                    <option value="">Tous les paiements</option>
                    <option value="unpaid" {{ 'selected' if request.args.get('payment_status') == 'unpaid' }}>Non payé</option>
                    <option value="partial" {{ 'selected' if request.args.get('payment_status') == 'partial' }}>Partiel</option>
                    <option value="paid" {{ 'selected' if request.args.get('payment_status') == 'paid' }}>Payé</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-1"></i>
                    Filtrer
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Liste des commandes -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Liste des commandes ({{ orders|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if orders %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>N° Commande</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Montant</th>
                        <th>Payé</th>
                        <th>Reste</th>
                        <th>Statut</th>
                        <th>Paiement</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders %}
                    <tr class="{{ 'table-warning' if order.payment_status == 'partial' else 'table-danger' if order.payment_status == 'unpaid' else '' }}">
                        <td>
                            <strong>{{ order.order_number }}</strong>
                        </td>
                        <td>
                            {% if order.customer %}
                                <div>
                                    <strong>{{ order.customer.name }}</strong>
                                    {% if order.customer.phone %}
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-phone fa-xs"></i>
                                        {{ order.customer.phone }}
                                    </small>
                                    {% endif %}
                                </div>
                            {% else %}
                                <span class="text-muted">Client anonyme</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                {{ order.created_at.strftime('%d/%m/%Y') }}
                                <br>
                                <small class="text-muted">{{ order.created_at.strftime('%H:%M') }}</small>
                            </div>
                        </td>
                        <td>
                            <strong>{{ "{:,.0f}".format(order.total_amount) }} DA</strong>
                        </td>
                        <td>
                            <span class="text-success">{{ "{:,.0f}".format(order.paid_amount) }} DA</span>
                        </td>
                        <td>
                            {% if order.remaining_amount > 0 %}
                                <span class="text-danger">{{ "{:,.0f}".format(order.remaining_amount) }} DA</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.status == 'completed' %}
                                <span class="badge bg-success">Terminé</span>
                            {% elif order.status == 'pending' %}
                                <span class="badge bg-warning">En attente</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-secondary">Annulé</span>
                            {% else %}
                                <span class="badge bg-info">{{ order.status.title() }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.payment_status == 'paid' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    Payé
                                </span>
                            {% elif order.payment_status == 'partial' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>
                                    Partiel
                                </span>
                            {% else %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>
                                    Non payé
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('order_detail', order_id=order.id) }}" class="btn btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('generate_invoice', order_id=order.id) }}" class="btn btn-outline-success" title="Facture PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <a href="{{ url_for('generate_order_slip', order_id=order.id) }}" class="btn btn-outline-info" title="Bon de commande">
                                    <i class="fas fa-file-alt"></i>
                                </a>
                                {% if order.payment_status != 'paid' %}
                                <button class="btn btn-outline-warning" onclick="showPaymentModal({{ order.id }}, {{ order.remaining_amount }})" title="Ajouter paiement">
                                    <i class="fas fa-money-bill"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune commande trouvée</h5>
            <p class="text-muted">Commencez par créer votre première vente</p>
            <a href="{{ url_for('sales') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Nouvelle vente
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal paiement rapide -->
<div class="modal fade" id="quickPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-money-bill me-2"></i>
                    Ajouter un paiement
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickPaymentForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Montant restant</label>
                        <input type="text" class="form-control" id="remainingAmount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Montant à payer</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="amount" id="paymentAmount" step="0.01" min="0" required>
                            <span class="input-group-text">DA</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Méthode de paiement</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="cash">Espèces</option>
                            <option value="card">Carte bancaire</option>
                            <option value="transfer">Virement</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Référence (optionnel)</label>
                        <input type="text" class="form-control" name="reference" placeholder="Numéro de transaction, etc.">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" name="notes" rows="2" placeholder="Notes sur le paiement..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        Enregistrer le paiement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showPaymentModal(orderId, remainingAmount) {
    const modal = new bootstrap.Modal(document.getElementById('quickPaymentModal'));
    const form = document.getElementById('quickPaymentForm');
    
    // Configurer le formulaire
    form.action = `/orders/${orderId}/payment`;
    document.getElementById('remainingAmount').value = remainingAmount.toLocaleString() + ' DA';
    document.getElementById('paymentAmount').value = remainingAmount;
    document.getElementById('paymentAmount').max = remainingAmount;
    
    modal.show();
}

// Validation du formulaire de paiement
document.getElementById('quickPaymentForm').addEventListener('submit', function(e) {
    const amount = parseFloat(document.getElementById('paymentAmount').value) || 0;
    const maxAmount = parseFloat(document.getElementById('paymentAmount').max) || 0;
    
    if (amount <= 0) {
        alert('Le montant doit être supérieur à 0');
        e.preventDefault();
        return;
    }
    
    if (amount > maxAmount) {
        alert('Le montant ne peut pas dépasser le montant restant');
        e.preventDefault();
        return;
    }
});

// Mise à jour automatique des données toutes les 30 secondes
setInterval(function() {
    // Recharger seulement si aucun modal n'est ouvert
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 30000);

// Animation des lignes selon le statut de paiement
document.addEventListener('DOMContentLoaded', function() {
    const unpaidRows = document.querySelectorAll('.table-danger');
    const partialRows = document.querySelectorAll('.table-warning');
    
    // Animation subtile pour les commandes non payées
    unpaidRows.forEach(row => {
        row.style.borderLeft = '4px solid #dc3545';
    });
    
    partialRows.forEach(row => {
        row.style.borderLeft = '4px solid #ffc107';
    });
});
</script>
{% endblock %}
