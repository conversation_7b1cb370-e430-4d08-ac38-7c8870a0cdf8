from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from datetime import datetime
import os

class PDFGenerator:
    def __init__(self, company_info=None):
        self.company_info = company_info or {
            'name': 'Magasin de Meubles Premium',
            'address': '123 Rue des Meubles, Ville, Pays',
            'phone': '+213 XX XX XX XX XX',
            'email': '<EMAIL>'
        }
        
    def generate_invoice(self, order, filename=None):
        """Générer une facture PDF pour une commande"""
        if not filename:
            filename = f"reports/facture_{order.order_number}.pdf"
        
        # Créer le document
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # Style personnalisé pour le titre
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        # En-tête de l'entreprise
        story.append(Paragraph(self.company_info['name'], title_style))
        story.append(Paragraph(self.company_info['address'], styles['Normal']))
        story.append(Paragraph(f"Tél: {self.company_info['phone']}", styles['Normal']))
        story.append(Paragraph(f"Email: {self.company_info['email']}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Titre de la facture
        invoice_title = ParagraphStyle(
            'InvoiceTitle',
            parent=styles['Heading2'],
            fontSize=18,
            alignment=TA_CENTER,
            textColor=colors.darkred
        )
        story.append(Paragraph("FACTURE", invoice_title))
        story.append(Spacer(1, 20))
        
        # Informations de la commande
        order_info = [
            ['Numéro de commande:', order.order_number],
            ['Date:', order.created_at.strftime('%d/%m/%Y %H:%M')],
            ['Client:', order.customer.name if order.customer else 'Client anonyme'],
            ['Statut:', order.status.upper()],
        ]
        
        if order.customer and order.customer.phone:
            order_info.append(['Téléphone:', order.customer.phone])
        if order.customer and order.customer.address:
            order_info.append(['Adresse:', order.customer.address])
        
        order_table = Table(order_info, colWidths=[2*inch, 4*inch])
        order_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(order_table)
        story.append(Spacer(1, 20))
        
        # Tableau des articles
        items_data = [['Article', 'Quantité', 'Prix unitaire', 'Total']]
        
        for item in order.items:
            items_data.append([
                item.product.name,
                str(item.quantity),
                f"{item.unit_price:,.2f} DA",
                f"{item.total_price:,.2f} DA"
            ])
        
        # Ligne de sous-total
        subtotal = sum(item.total_price for item in order.items)
        items_data.append(['', '', 'Sous-total:', f"{subtotal:,.2f} DA"])
        
        if order.discount_amount > 0:
            items_data.append(['', '', 'Remise:', f"-{order.discount_amount:,.2f} DA"])
        
        if order.tax_amount > 0:
            items_data.append(['', '', 'TVA:', f"{order.tax_amount:,.2f} DA"])
        
        items_data.append(['', '', 'TOTAL:', f"{order.total_amount:,.2f} DA"])
        
        items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, -3), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
        ]))
        story.append(items_table)
        story.append(Spacer(1, 20))
        
        # Informations de paiement
        if order.payments.count() > 0:
            story.append(Paragraph("PAIEMENTS", styles['Heading3']))
            payments_data = [['Date', 'Montant', 'Méthode', 'Référence']]
            
            for payment in order.payments:
                payments_data.append([
                    payment.created_at.strftime('%d/%m/%Y'),
                    f"{payment.amount:,.2f} DA",
                    payment.payment_method.upper(),
                    payment.reference or '-'
                ])
            
            payments_data.append(['', f"Total payé: {order.paid_amount:,.2f} DA", '', ''])
            payments_data.append(['', f"Reste à payer: {order.remaining_amount:,.2f} DA", '', ''])
            
            payments_table = Table(payments_data, colWidths=[1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch])
            payments_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('BACKGROUND', (0, 1), (-1, -3), colors.beige),
                ('GRID', (0, 0), (-1, -3), 1, colors.black),
                ('BACKGROUND', (0, -2), (-1, -1), colors.lightgrey),
                ('FONTNAME', (0, -2), (-1, -1), 'Helvetica-Bold'),
                ('SPAN', (1, -2), (3, -2)),
                ('SPAN', (1, -1), (3, -1)),
            ]))
            story.append(payments_table)
        
        story.append(Spacer(1, 30))
        
        # Pied de page
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Merci pour votre confiance!", footer_style))
        story.append(Paragraph(f"Facture générée le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", footer_style))
        
        # Construire le PDF
        doc.build(story)
        return filename
    
    def generate_order_slip(self, order, filename=None):
        """Générer un bon de commande PDF"""
        if not filename:
            filename = f"reports/bon_commande_{order.order_number}.pdf"
        
        # Utiliser la même structure que la facture mais avec un titre différent
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # En-tête
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        story.append(Paragraph(self.company_info['name'], title_style))
        story.append(Spacer(1, 20))
        
        # Titre du bon
        slip_title = ParagraphStyle(
            'SlipTitle',
            parent=styles['Heading2'],
            fontSize=18,
            alignment=TA_CENTER,
            textColor=colors.darkgreen
        )
        story.append(Paragraph("BON DE COMMANDE", slip_title))
        story.append(Spacer(1, 20))
        
        # Informations de base (similaire à la facture)
        order_info = [
            ['Numéro:', order.order_number],
            ['Date:', order.created_at.strftime('%d/%m/%Y %H:%M')],
            ['Client:', order.customer.name if order.customer else 'Client anonyme'],
        ]
        
        order_table = Table(order_info, colWidths=[2*inch, 4*inch])
        order_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        story.append(order_table)
        story.append(Spacer(1, 20))
        
        # Articles commandés
        items_data = [['Article', 'Quantité', 'Prix unitaire', 'Total']]
        
        for item in order.items:
            items_data.append([
                item.product.name,
                str(item.quantity),
                f"{item.unit_price:,.2f} DA",
                f"{item.total_price:,.2f} DA"
            ])
        
        items_data.append(['', '', 'TOTAL:', f"{order.total_amount:,.2f} DA"])
        
        items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, 1), (-1, -2), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, -1), (-1, -1), colors.grey),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
        ]))
        story.append(items_table)
        
        story.append(Spacer(1, 40))
        
        # Signatures
        signature_data = [
            ['Signature du client:', 'Signature du vendeur:'],
            ['', ''],
            ['', ''],
            ['Date: ___________', 'Date: ___________']
        ]
        
        signature_table = Table(signature_data, colWidths=[3*inch, 3*inch])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        story.append(signature_table)
        
        # Construire le PDF
        doc.build(story)
        return filename
