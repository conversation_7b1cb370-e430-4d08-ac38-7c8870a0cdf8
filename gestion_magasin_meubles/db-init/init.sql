-- Initialisation de la base de données pour le magasin de meubles
USE magasin_meubles;

-- Insertion des catégories par défaut
INSERT INTO categories (name, description) VALUES
('Salon', 'Meubles de salon: canapés, fauteuils, tables basses'),
('Chambre', 'Meubles de chambre: lits, armoires, commodes'),
('Cuisine', 'Meubles de cuisine: tables, chaises, buffets'),
('Bureau', 'Meubles de bureau: bureaux, chaises de bureau, étagères'),
('Salle à manger', 'Meubles de salle à manger: tables, chaises, buffets'),
('Décoration', 'Articles de décoration et accessoires');

-- Insertion des paramètres par défaut
INSERT INTO settings (key, value, description) VALUES
('company_name', 'Magasin de Meubles Premium', 'Nom de l\'entreprise'),
('company_address', '123 Rue des Meubles, Ville, Pays', '<PERSON>resse de l\'entreprise'),
('company_phone', '+213 XX XX XX XX XX', 'Téléphone de l\'entreprise'),
('company_email', '<EMAIL>', 'Email de l\'entreprise'),
('tax_rate', '19', 'Taux de TVA par défaut (%)'),
('currency', 'DA', 'Devise utilisée'),
('low_stock_threshold', '5', 'Seuil d\'alerte stock bas'),
('order_prefix', 'CMD', 'Préfixe des numéros de commande');

-- Insertion de quelques produits d'exemple
INSERT INTO products (code, name, description, category_id, purchase_price, sale_price, stock_quantity, min_stock) VALUES
('CANAPE001', 'Canapé 3 places en cuir', 'Canapé confortable en cuir véritable, couleur marron', 1, 45000.00, 75000.00, 5, 2),
('LIT001', 'Lit double 160x200', 'Lit double avec tête de lit rembourrée', 2, 25000.00, 42000.00, 8, 3),
('TABLE001', 'Table à manger 6 personnes', 'Table en bois massif pour 6 personnes', 5, 18000.00, 32000.00, 4, 2),
('CHAISE001', 'Chaise de salle à manger', 'Chaise en bois avec assise rembourrée', 5, 3500.00, 6500.00, 20, 5),
('ARMOIRE001', 'Armoire 3 portes', 'Grande armoire avec miroir central', 2, 35000.00, 58000.00, 3, 1);

-- Insertion d'un client exemple
INSERT INTO customers (name, phone, email, address) VALUES
('Client Exemple', '0555123456', '<EMAIL>', '456 Rue du Client, Ville');

-- Insertion de quelques frais fixes
INSERT INTO expenses (name, amount, category, description, date, is_recurring) VALUES
('Loyer magasin', 50000.00, 'rent', 'Loyer mensuel du magasin', CURDATE(), true),
('Électricité', 8000.00, 'utilities', 'Facture d\'électricité', CURDATE(), true),
('Assurance', 12000.00, 'insurance', 'Assurance du magasin', CURDATE(), true);
