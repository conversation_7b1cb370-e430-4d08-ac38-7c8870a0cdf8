events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Configuration upstream pour Frappe
    upstream frappe_server {
        server frappe:8000;
    }

    upstream socketio_server {
        server frappe:9000;
    }

    # Configuration du serveur principal
    server {
        listen 80;
        server_name localhost gestion-meubles.local;

        # Taille maximale des uploads
        client_max_body_size 100M;

        # Proxy vers Frappe
        location / {
            proxy_pass http://frappe_server;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
        }

        # WebSocket pour Socket.IO
        location /socket.io/ {
            proxy_pass http://socketio_server;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
